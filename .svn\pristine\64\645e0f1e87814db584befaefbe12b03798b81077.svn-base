import Vue from 'vue'
import App from './App.vue'
import router from './router'
import vuetify from './plugins/vuetify'
import day from 'dayjs'
import './plugins/http'

Vue.filter('dateformat', function(indate, outdate) {
  return day(indate).format(outdate)
})
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
Vue.use(ElementUI);

new Vue({
  router,
  vuetify,
  el: '#app',
  render: h => h(App)
});

Vue.config.productionTip = false

