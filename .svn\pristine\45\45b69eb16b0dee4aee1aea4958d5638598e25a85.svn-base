package middleware

import (
	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
)

type SqlLiteClient struct {
	DB *gorm.DB
}

func (this *SqlLiteClient) InitDB(dns string) {
	db, err := gorm.Open(sqlite.Open(dns), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	// 启用 WAL 模式
	_ = db.Exec("PRAGMA journal_mode=WAL;")
	sqlDB, err := db.DB()
	if err != nil {
		panic("failed to connect database")
	}
	sqlDB.SetMaxIdleConns(10)
	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(50)
	this.DB = db
}
