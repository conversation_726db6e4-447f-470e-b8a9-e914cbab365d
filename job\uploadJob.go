package job

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/minio/minio-go/v7"
	"github.com/wejectchen/ginblog/middleware"
	"github.com/wejectchen/ginblog/service"
	"github.com/wejectchen/ginblog/utils"
	"golang.org/x/image/tiff"
	"image/jpeg"
	"io/ioutil"
	"log"
	"net"
	"net/url"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

type UploadClient struct {
	DbId           string //数据库ID
	FileServerIp   string //文件服务器IP地址
	FileServerPort string //文件服务器端口
	Ip             string
	FileServerType string //文件服务器类型  oss:阿里oss;minio:minio;
	OSSAccessKeyId string
	HttpClient     *middleware.RestyClient
	MinIOClient    *middleware.MinioClient
	OSSClient      *middleware.OSSClient
	IsLogin        bool   //是否登录
	BucketName     string //bucketName
}

var G_SqlLiteClient *middleware.SqlLiteClient
var G_DbUploadClientMap map[string]*UploadClient
var G_CNT = 0
var G_Clients map[string]*UploadClient

type UploadJob struct {
	running bool
}

type ScanCandidateSimple struct {
	SubjectId   int    `json:"subjectId"`
	CandidateId int    `json:"candidateId"`
	CandidateNo string `json:"candidateNo"`
	ScanMac     string `json:"scanMac"`
	ImgState    int    `json:"imgState"`
	ScanBatchNo string `json:"scanBatchNo"`
}

type UploadCandidate struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data []struct {
		SubjectId   int    `json:"subjectId"`
		CandidateId int    `json:"candidateId"`
		CandidateNo string `json:"candidateNo"`
		ScanMac     string `json:"scanMac"`
		ImgState    int    `json:"imgState"`
		ScanBatchNo string `json:"scanBatchNo"`
	} `json:"data"`
}

type SysConfigList struct {
	Data []SysConfig `json:"data"`
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
}

type SysConfigs struct {
	Rows []SysConfig `json:"rows"`
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
}

type SysConfig struct {
	CreateBy    string `json:"createBy"`
	UpdateBy    string `json:"updateBy"`
	Remark      string `json:"remark"`
	ConfigId    int    `json:"configId"`
	ConfigName  string `json:"configName"`
	ConfigKey   string `json:"configKey"`
	ConfigValue string `json:"configValue"`
	ConfigType  string `json:"configType"`
	Category    string `json:"category"`
	PId         int    `json:"pId"`
	ReadOnly    string `json:"readOnly"`
}

// 取系统参数
func (this *UploadJob) getSysConfigMap() (map[string]*UploadClient, error) {
	url := "/system/config/getConfigs/byPIdKeyList"
	result := make(map[string]*UploadClient, 0)
	for k, _ := range G_Clients {
		if !G_Clients[k].IsLogin {
			fmt.Println("服务器:" + G_Clients[k].Ip + "未登录，请确认服务器能正常连接")
			utils.Log.Error("服务器:" + G_Clients[k].Ip + "未登录，请确认服务器能正常连接")
			continue
		}
		configs := []SysConfig{
			{PId: 0, ConfigKey: "scan.exam.dbid"},
			{PId: 0, ConfigKey: "scan.subject.ScanImgFTPIP"},
			{PId: 0, ConfigKey: "scan.img.port"},
			{PId: 0, ConfigKey: "file.store.type"},
			{PId: 0, ConfigKey: "oss.access.key"},
			{PId: 0, ConfigKey: "img.buck.name"},
		}
		client := G_Clients[k].HttpClient
		resp, err := client.PostBodyList(url, configs)
		if err != nil {
			fmt.Println("服务器:"+G_Clients[k].Ip+"取dbid出错", err)
			utils.Log.Error("服务器:" + G_Clients[k].Ip + "取dbid出错" + err.Error())
			continue
		} else {
			var configList SysConfigList
			err := json.Unmarshal(resp.Body(), &configList)
			if err != nil {
				fmt.Println("get db id list error", err)
				utils.Log.Error("get db id list error" + err.Error())
				continue
			} else {
				for i := range configList.Data {
					if configList.Data[i].ConfigKey == "scan.exam.dbid" {
						G_Clients[k].DbId = configList.Data[i].ConfigValue
						if G_Clients[k].DbId == "" {
							fmt.Println("服务器:" + G_Clients[k].Ip + " db id为空，请确认服务器能正常连接")
							utils.Log.Info("服务器:" + G_Clients[k].Ip + " db id为空，请确认服务器能正常连接")
							continue
						}
					}
					if configList.Data[i].ConfigKey == "scan.subject.ScanImgFTPIP" {
						G_Clients[k].FileServerIp = configList.Data[i].ConfigValue
						if configList.Data[i].ConfigValue == "" {
							panic("扫描图像服务器地址不能为空")
						}
					}
					if configList.Data[i].ConfigKey == "scan.img.port" {
						G_Clients[k].FileServerPort = configList.Data[i].ConfigValue
					}
					if configList.Data[i].ConfigKey == "file.store.type" {
						if configList.Data[i].ConfigValue == "" {
							log.Println("服务器:" + G_Clients[k].Ip + "扫描图像服务器类型没有配置，请配置")
							utils.Log.Info("服务器:" + G_Clients[k].Ip + "扫描图像服务器类型没有配置，请配置")
							continue
						}
						G_Clients[k].FileServerType = configList.Data[i].ConfigValue
					}
					if configList.Data[i].ConfigKey == "img.buck.name" {
						if configList.Data[i].ConfigValue == "" {
							log.Println("服务器:" + G_Clients[k].Ip + "图像buckName未配置，请配置")
							utils.Log.Info("服务器:" + G_Clients[k].Ip + "图像buckName未配置，请配置")
							continue
						}
						G_Clients[k].BucketName = configList.Data[i].ConfigValue
					}
					//if configList.Data[i].ConfigKey == "oss.access.key" {
					//	if configList.Data[i].ConfigValue == "" {
					//		panic("oss.access.key is empty")
					//	}
					G_Clients[k].OSSAccessKeyId = "tm3D3eGJj7yKVnU8Ud4wgByA5XBpV1"
					//}
				}
				result[G_Clients[k].DbId] = G_Clients[k]
			}
		}
	}
	return result, nil
}

type Exam struct {
	ExamId     int    `json:"examId"`
	ExamName   string `json:"examName"`
	CreateTime string `json:"createTime"`
	ErrorMsg   string `json:"errorMsg"`
	Error      int    `json:"error"`
}

type ExamResult struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Rows []Exam `json:"rows"`
}

func (this *UploadJob) getExamList(dbIdMap map[string]*UploadClient) map[string][]Exam {
	dbIdExamMap := make(map[string][]Exam, 0)
	for s := range dbIdMap {
		client := dbIdMap[s].HttpClient
		params := make(map[string]string)
		params["pageNum"] = "1"
		params["pageSize"] = "100000"
		params["finished"] = "1"
		params["disableUploadImg"] = "0"
		result, err := client.Get("/base/exam/list", params)
		if err != nil {
			fmt.Println("get exam list error1")
			utils.Log.Error("get exam list error1")
			return nil
		}
		var examResult ExamResult
		err = json.Unmarshal(result.Body(), &examResult)
		if err != nil {
			fmt.Println("get exam list error2")
			utils.Log.Error("get exam list error2")
			return nil
		}
		if examResult.Code == 200 {
			dbIdExamMap[s] = examResult.Rows
		} else {
			fmt.Println("get exam list error3")
			utils.Log.Error("get exam list error3")
			return nil
		}
	}
	return dbIdExamMap
}

//func getNeedUploadList(mac string, subjectId string, client UploadClient) (UploadCandidate, error) {
//	var uploadCandidate UploadCandidate
//	result, err := client.HttpClient.Get("/candidate/scan/getNeedUploadCandidate", map[string]string{"mac": mac, "subjectId": subjectId})
//	if err != nil {
//		fmt.Println("get need upload list error")
//		return uploadCandidate, err
//	}
//
//	err = json.Unmarshal(result.Body(), &uploadCandidate)
//	if err != nil {
//		fmt.Println("get need upload list error", err)
//		return uploadCandidate, err
//	}
//	return uploadCandidate, nil
//}

type ImageInfo struct {
	Id          int    `gorm:"id"`
	SubjectId   string `gorm:"subjectId"`
	SubjectName string `gorm:"subjectName"`
	BatchNo     string `gorm:"batchNo"`
	CandidateNo string `json:"candidateNo"`
	CardType    string `json:"cardType"`
	FImageUrl   string `gorm:"fImageUrl"`
	BImageUrl   string `gorm:"bImageUrl"`
	FMd5        string `json:"fMd5"`
	BMd5        string `json:"bMd5"`
	ScanUser    string `json:"scanUser"`
	CreateTime  string `json:"createTime"`
	FETag       string `json:"fETag"`
	BETag       string `json:"bETag"`
	UploadType  string `json:"uploadType"`
	ItemId      string `json:"itemId"`
}

type RecordCnt struct {
	Cnt int `gorm:"G_CNT"`
}

func (this *UploadJob) UploadTest() {
	dbUploadClientMap, err := this.getSysConfigMap()
	if err != nil {
		utils.Log.Error("服务器连接不上")
		log.Println("服务器连接不上！")
		return
	}
	if dbUploadClientMap == nil || len(dbUploadClientMap) == 0 {
		return
	}

	var imgList []ImageInfo
	for i := 0; i < 50000; i++ {
		var img ImageInfo
		img.SubjectId = "1"
		img.BatchNo = "1"
		img.CandidateNo = "candidateNo" + strconv.Itoa(i)
		img.CardType = "1"
		img.FImageUrl = "candidateNo" + strconv.Itoa(i)
		img.BImageUrl = "candidateNo" + strconv.Itoa(i)
		img.FMd5 = "1"
		img.BMd5 = "1"
		img.ScanUser = "1"
		imgList = append(imgList, img)
		if len(imgList) >= 80 {
			this.insertBatch(imgList)
			imgList = make([]ImageInfo, 0)
		}
	}

}

func (this *UploadJob) insertBatch(imgList []ImageInfo) {
	sql := " INSERT INTO db_abcd_36_undo( subject_id,subject_name, batch_no, candidate_no, cardtype, f_image_url, b_image_url, f_md5, b_md5, scan_user, create_time,upload_type,item_id )  "
	for i := range imgList {
		info := imgList[i]
		tmpStr := "select " + info.SubjectId + " ,'" + info.SubjectName + "' ,'" + info.BatchNo + "'','" + info.CandidateNo + "','" + info.CardType + "' ,'" + info.FImageUrl + "','" +
			info.BImageUrl + "','" + info.FMd5 + "','" + info.BMd5 + "' ,'" + info.ScanUser + "' ,now() ," + info.UploadType + "," + info.ItemId
		if i == 0 {
			sql = sql + tmpStr
		} else {
			sql = sql + " union all  select " + tmpStr
		}
	}
	err := G_SqlLiteClient.DB.Raw(sql).Error
	if err != nil {
		utils.Log.Error(" insert db error:" + err.Error())
		fmt.Println(" insert db error")
	}
}

func (this *UploadJob) existTable(tableName string) bool {
	var count int64
	err := G_SqlLiteClient.DB.Raw("select count(1) from sqlite_master where type='table' and name = ?", tableName).Count(&count).Error
	if err != nil || count == 0 {
		return false
	} else {
		return true
	}
}

func (this *UploadJob) loginHandle() {
	if G_Clients == nil {
		G_Clients = make(map[string]*UploadClient)
	}
	for i := range utils.ServerUrls {
		serverUrl := utils.ServerUrls[i]
		if G_Clients[utils.ServerUrls[i]] == nil {
			httpClient := middleware.RestyClient{
				Url: serverUrl + "/api",
			}
			httpClient.NewClient()
			var uploadClient = UploadClient{
				Ip:         serverUrl,
				HttpClient: &httpClient,
				IsLogin:    false,
			}
			G_Clients[serverUrl] = &uploadClient
		}
		if G_Clients[serverUrl].IsLogin == false {
			restyClient := G_Clients[utils.ServerUrls[i]].HttpClient
			login := service.DoLogin(restyClient)
			G_Clients[serverUrl].IsLogin = login
		}
	}
}
func (this *UploadJob) stop() {
	this.running = false
}

func (this *UploadJob) initMinioBucket(client *UploadClient) {
	bucketName := client.BucketName
	client.MinIOClient.Client.MakeBucket(context.Background(), bucketName, minio.MakeBucketOptions{Region: "us-east-1"})
	// 设置 Bucket 策略为公共读
	policy := `{
			"Version": "2012-10-17",
			"Statement": [
				{
					"Effect": "Allow",
					"Principal": {
						"AWS": [
							"*"
						]
					},
					"Action": [
						"s3:GetObject"
					],
					"Resource": [
						"arn:aws:s3:::` + client.BucketName + `/*"
					]
				}
			]
		}`
	client.MinIOClient.Client.SetBucketPolicy(context.Background(), bucketName, policy)
}

func (this *UploadJob) extractFirstNode(urlStr string) (string, error) {
	// 查找第一个点，并提取其之前的部分作为第一个节点
	firstDotIndex := strings.Index(urlStr, ".")
	if firstDotIndex == -1 {
		// 如果没有找到点，则整个主机名就是我们要找的第一个节点（尽管这通常不是一个子域名的情况）
		return "", nil
	}

	// 提取第一个节点
	firstNode := urlStr[:firstDotIndex]
	return firstNode, nil
}

func (this *UploadJob) Upload() {
	if this.running {
		return
	}
	this.running = true
	defer this.stop()

	this.loginHandle()
	var err error

	if G_DbUploadClientMap == nil {
		G_DbUploadClientMap, err = this.getSysConfigMap()
		if err != nil {
			fmt.Println("服务器连接不上！")
			utils.Log.Error("服务器连接不上！" + err.Error())
			return
		}
		if G_DbUploadClientMap == nil || len(G_DbUploadClientMap) == 0 {
			return
		}
		for s := range G_Clients {
			parsedURL, err := url.Parse(G_Clients[s].FileServerIp)
			if err != nil {
				fmt.Println("Error parsing URL:", err)
				return
			}
			serverUrl := parsedURL.Hostname()
			if G_Clients[s].FileServerPort != "" && G_Clients[s].FileServerType != "oss" {
				serverUrl = parsedURL.Hostname() + ":" + G_Clients[s].FileServerPort
			}
			serverUrl = strings.Replace(serverUrl, G_Clients[s].BucketName+".", "", -1)
			if G_Clients[s].FileServerType == "oss" {
				var client = middleware.OSSClient{
					Endpoint:        serverUrl,
					AccessKeyID:     "LTAI5tRPX95iiUsHxVmwEuhV",
					SecretAccessKey: G_Clients[s].OSSAccessKeyId,
					BucketName:      G_Clients[s].BucketName,
				}
				G_Clients[s].OSSClient = &client
				client.InitClient()
			}
			if G_Clients[s].FileServerType == "minio" {
				var client = middleware.MinioClient{
					Endpoint:        serverUrl,
					AccessKeyID:     "hytadmin",
					SecretAccessKey: "sky@2023",
					UseSSL:          false,
				}
				G_Clients[s].MinIOClient = &client
				client.InitMinioClient()
			}
		}
	}

	if G_DbUploadClientMap == nil || len(G_DbUploadClientMap) == 0 {
		return
	}

	G_CNT = G_CNT + 1
	if G_CNT > 20 {
		G_DbUploadClientMap = nil
		G_CNT = 0
	}

	dbIdExamMap := this.getExamList(G_DbUploadClientMap)

	this.uploadByType(dbIdExamMap, "0")
	this.uploadByType(dbIdExamMap, "1")
}

// uploadType 0扫描 1切割
func (this *UploadJob) uploadByType(dbIdExamMap map[string][]Exam, uploadType string) {
	for s := range dbIdExamMap {
		dbMap := dbIdExamMap[s]
		for i := range dbMap {
			tableNameDo := "db_" + s + "_" + strconv.Itoa(dbMap[i].ExamId) + "_do"
			tableNameUnDo := "db_" + s + "_" + strconv.Itoa(dbMap[i].ExamId) + "_undo"
			tableNameErr := "db_" + s + "_" + strconv.Itoa(dbMap[i].ExamId) + "_error"
			if !this.existTable(tableNameUnDo) {
				continue
			}
			var imageInfoBatch []ImageInfo
			err := G_SqlLiteClient.DB.Raw(" select distinct a.batch_no from " + tableNameUnDo +
				"  a left join " + tableNameDo + " b on a.id=b.id and a.subject_id=b.subject_id left join " + tableNameErr +
				" c on a.id=c.id and a.subject_id=c.subject_id where b.id is null and c.id is null and a.upload_type=" + uploadType + " order by a.create_time limit 40").Scan(&imageInfoBatch).Error
			if err != nil {
				fmt.Println("get image info error", err)
				utils.Log.Error("get image info error" + err.Error())
				continue
			}
			if imageInfoBatch == nil || len(imageInfoBatch) == 0 {
				this.errorRecordHandle(tableNameErr)
				continue
			}

			var imageInfos []ImageInfo
			err = G_SqlLiteClient.DB.Raw(" select a.* from " + tableNameUnDo +
				"  a left join " + tableNameDo + " b on a.id=b.id and a.subject_id=b.subject_id left join " + tableNameErr +
				" c on a.id=c.id and a.subject_id=c.subject_id where b.id is null and c.id is null and a.upload_type=" + uploadType + " and a.batch_no='" +
				imageInfoBatch[0].BatchNo + "' order by a.candidate_no").Scan(&imageInfos).Error
			if err != nil {
				fmt.Println("get image info error", err)
				utils.Log.Error("get image info error" + err.Error())
				continue
			}
			if imageInfos == nil || len(imageInfos) == 0 {
				this.errorRecordHandle(tableNameErr)
				continue
			}
			client, ok := G_DbUploadClientMap[s]
			if !ok {
				continue
			}
			doUploadImgBySqlLite(client, imageInfos, G_SqlLiteClient, tableNameDo, tableNameErr, uploadType)
		}
	}
}

// 把错误表中，4分钟内没有上传成功的记录删除掉
func (this *UploadJob) errorRecordHandle(tableNameErr string) {
	var cnt int
	now := time.Now()
	nowUnix := now.Unix()
	fiveMinutesAgoUnix := nowUnix - 4*60
	timeFromUnix := time.Unix(fiveMinutesAgoUnix, 0) // 纳秒部分设置为0
	formattedTime := timeFromUnix.Format("2006-01-02 15:04:05")
	err := G_SqlLiteClient.DB.Raw("select count(1) from " + tableNameErr + " where (julianday('" + formattedTime + "')-julianday(create_time))*24*60>4").Scan(&cnt).Error
	if err != nil {
		fmt.Println("get image count error", err)
		utils.Log.Error("get image count error" + err.Error())
	} else {
		if cnt > 0 {
			err := G_SqlLiteClient.DB.Exec("delete from " + tableNameErr + " where (julianday('" + formattedTime + "')-julianday(create_time))*24*60>4").Error
			if err != nil {
				fmt.Println("delete error table error", err)
				utils.Log.Error("delete error table error" + err.Error())
			}
		}
	}
}

var errorMap = make(map[string]int)

func doUploadImgBySqlLite(client *UploadClient, imgList []ImageInfo, sqlLiteClient *middleware.SqlLiteClient, tableNameDo, tableNameErr string, uploadType string) {
	groupedByCandidateNo := make(map[string][]ImageInfo)
	// 遍历imgList，根据CandidateNo分组
	for _, item := range imgList {
		groupedByCandidateNo[item.CandidateNo] = append(groupedByCandidateNo[item.CandidateNo], item)
	}

	for _, items := range groupedByCandidateNo {
		success := true
		var err1, err2, err3, err4 error
		var fETag, bETag string
		for i := range items {
			path := items[i].FImageUrl
			if client.FileServerType == "oss" {
				var jpgFileName string
				err3, jpgFileName = saveToJpg(path)
				if err3 != nil {
					println("saveToJpg error", err3)
					utils.Log.Error("saveToJpg error" + err3.Error())
					success = false
					break
				}
				err1, fETag = uploadOneImgToOss(client, path, "")
				err3, _ = uploadOneImgToOss(client, jpgFileName, "j")
			} else {
				err1, fETag = uploadOneImgToMinIO(client, path)
			}
			path = items[i].BImageUrl
			if client.FileServerType == "oss" {
				var jpgFileName string
				err4, jpgFileName = saveToJpg(path)
				if err4 != nil {
					println("saveToJpg error", err3)
					utils.Log.Error("saveToJpg error" + err3.Error())
					success = false
					break
				}
				err2, bETag = uploadOneImgToOss(client, path, "")
				err4, _ = uploadOneImgToOss(client, jpgFileName, "j")
			} else {
				err2, bETag = uploadOneImgToMinIO(client, path)
			}
			info := items[i]
			if err1 == nil && err2 == nil && err3 == nil && err4 == nil {
				info.FETag = fETag
				info.BETag = bETag
			} else {
				success = false
				break
			}
		}
		errorHandle(err1, err2, err3, err4, tableNameDo, items, sqlLiteClient, tableNameErr)
		if !success {
			continue
		}
		info := items[0]
		//扫描数据
		if uploadType == "0" {
			serverUrl := ""
			if client.FileServerType == "oss" {
				serverUrl = client.OSSClient.Endpoint
			} else {
				serverUrl = client.MinIOClient.Endpoint
			}
			err3 := service.UpdateUploadStatus(client.HttpClient, info.CandidateNo, serverUrl, info.SubjectId, info.BatchNo)
			if err3 != nil {
				errorHandle(nil, nil, err3, nil, tableNameDo, items, sqlLiteClient, tableNameErr)
				continue
			}
		} else { //切割数据

		}

		serverUrl := client.FileServerIp
		if client.FileServerPort != "" {
			serverUrl = client.FileServerIp + ":" + client.FileServerPort
		}

		successInsertToDb(tableNameDo, items, sqlLiteClient, serverUrl)
	}
}

func successInsertToDb(tableNameDo string, items []ImageInfo, sqlLiteClient *middleware.SqlLiteClient, serverUrl string) {
	sql := " INSERT INTO  " + tableNameDo +
		"(id, subject_id,subject_name, batch_no, candidate_no, card_type, f_image_url, b_image_url, f_md5, b_md5, " +
		"scan_user, create_time,upload_type,item_id ,server_url) "
	for i := range items {
		info := items[i]
		if info.ItemId == "" {
			info.ItemId = "0"
		}
		if i != 0 {
			sql = sql + " union all "
		}
		sql = sql +
			" select " + strconv.Itoa(info.Id) + "," + info.SubjectId + " ,'" + info.SubjectName + "','" + info.BatchNo + "','" + info.CandidateNo + "','" + info.CardType + "' ,'" + info.FImageUrl + "','" +
			info.BImageUrl + "','" + info.FETag + "','" + info.BETag + "' ,'" + info.ScanUser +
			"' ,datetime(CURRENT_TIMESTAMP,'localtime'), " + info.UploadType + "," + info.ItemId + ",'" + serverUrl + "'"
	}
	err := sqlLiteClient.DB.Exec(sql).Error
	if err != nil {
		fmt.Println(" insert db error", err)
		utils.Log.Error(" insert db error" + err.Error())
	} else {
		info := items[0]
		fmt.Println("上传成功", info.SubjectName, info.BatchNo, info.CandidateNo)
		utils.Log.Info("上传成功" + info.SubjectName + "  " + info.BatchNo + "  " + info.CandidateNo)
	}
}

func errorHandle(err1 error, err2 error, err3, err4 error, tableNameDo string, infos []ImageInfo, sqlLiteClient *middleware.SqlLiteClient, tableNameErr string) {
	errMsg := ""
	if err1 != nil {
		errMsg = err1.Error()
	}
	if err2 != nil {
		errMsg = errMsg + "  " + err2.Error()
	}
	if err3 != nil {
		errMsg = errMsg + "  " + err3.Error()
	}
	if err4 != nil {
		errMsg = errMsg + "  " + err4.Error()
	}
	if len(errorMap) < 10000 {
		key := tableNameDo + infos[0].SubjectId + infos[0].CandidateNo + infos[0].BatchNo
		v, ok := errorMap[key]
		if !ok {
			errorMap[key] = 1
		} else {
			errorMap[key] = v + 1
		}
		if errorMap[key] > 5 {
			fmt.Println("尝试5次上传失败，入错误表:", infos)
			utils.Log.Error("尝试5次上传失败，入错误表:" + key)
			err := insertToErrorTable(sqlLiteClient, infos, tableNameErr, errMsg)
			if err == nil {
				delete(errorMap, key)
			}
		}
	} else {
		insertToErrorTable(sqlLiteClient, infos, tableNameErr, errMsg)
	}
}

func insertToErrorTable(sqlLiteClient *middleware.SqlLiteClient, infos []ImageInfo, tableNameErr, errMsg string) error {
	var t ImageInfo
	err2 := sqlLiteClient.DB.Raw("select * from "+tableNameErr+" where id=?", infos[0].Id).Scan(&t).Error
	if err2 != nil {
		fmt.Println("查询出错", err2)
		utils.Log.Error("查询出错" + err2.Error())
		return err2
	}
	if t.Id == 0 {
		sql := " INSERT INTO  " + tableNameErr +
			"(id, subject_id,subject_name, batch_no, candidate_no, card_type, f_image_url, b_image_url, f_md5, b_md5, scan_user, create_time,error_msg,upload_type,item_id )   "
		for i := range infos {
			info := infos[i]
			if i != 0 {
				sql = sql + " union all "
			}
			sql += " select " + strconv.Itoa(info.Id) + "," + info.SubjectId + " ,'" + info.SubjectName + "','" + info.BatchNo + "','" + info.CandidateNo + "','" +
				info.CardType + "' ,'" + info.FImageUrl + "','" + info.BImageUrl + "','" + info.FMd5 + "','" +
				info.BMd5 + "' ,'" + info.ScanUser + "' ,datetime(CURRENT_TIMESTAMP,'localtime') " + " ,'" + errMsg + "', " + info.UploadType + "," + info.ItemId
		}
		err := sqlLiteClient.DB.Exec(sql).Error
		if err != nil {
			fmt.Println(" insert db error", err)
			utils.Log.Error(" insert db error" + err.Error())
			return err
		}
	}
	return nil
}

func saveToJpg(path string) (error, string) {
	// 打开TIFF文件
	tiffFile, err := os.Open(path)
	if err != nil {
		log.Println("无法打开TIFF文件: %v", err)
		utils.Log.Error("无法打开TIFF文件" + err.Error())
		return err, ""
	}
	defer tiffFile.Close()

	// 解码TIFF文件
	img, err := tiff.Decode(tiffFile)
	if err != nil {
		log.Println("无法解码TIFF文件: %v", err)
		utils.Log.Error("无法解码TIFF文件" + err.Error())
	}

	fileName := filepath.Base(path)
	ext := filepath.Ext(fileName)
	name := fileName[:len(fileName)-len(ext)]

	// 新文件路径
	newFilePath := filepath.Join(filepath.Dir(path), name+".jpg")

	// 创建JPG文件
	jpgFile, err := os.Create(newFilePath)
	if err != nil {
		log.Fatalf("无法创建JPG文件: %v", err)
		utils.Log.Error("无法创建JPG文件" + err.Error())
		return err, ""
	}
	defer jpgFile.Close()

	// 将图像编码为JPG格式并保存到文件
	err = jpeg.Encode(jpgFile, img, nil)
	if err != nil {
		log.Fatalf("无法将图像编码为JPG格式: %v", err)
		utils.Log.Error("无法将图像编码为JPG格式" + err.Error())
		return err, ""
	}
	return nil, newFilePath
}

func uploadOneImgToOss(client *UploadClient, path string, prefix string) (error, string) {
	fullName := path
	lastIndex := strings.LastIndex(fullName, "ScanImg")
	subPath := ""
	if lastIndex > 0 {
		subPath = fullName[lastIndex:]
	} else {
		utils.Log.Error("文件路径错误" + fullName)
		return errors.New("文件路径错误"), ""
	}
	subPath = strings.ReplaceAll(subPath, "\\", "/")
	if prefix != "" {
		subPath = prefix + "/" + subPath
	}
	err, md5 := client.OSSClient.PutObjectAndValidateMd5(subPath, fullName)
	if err != nil {
		println("上传失败", err)
		utils.Log.Error("上传失败" + err.Error())
		return err, ""
	}
	return nil, md5
}

func uploadOneImgToMinIO(client *UploadClient, path string) (error, string) {
	fullName := path
	fileTemp, err := os.Open(fullName)
	if err != nil {
		log.Println(err)
		return err, ""
	}
	defer fileTemp.Close()
	fileInfo, err := fileTemp.Stat()
	if err != nil {
		log.Println(err)
		utils.Log.Error("获取文件信息失败" + err.Error())
		return err, ""
	}
	bucketName := client.BucketName
	ETag, err := calculateETagFromContent(fullName)
	if err != nil {
		log.Println(err)
		utils.Log.Error("计算文件md5失败" + err.Error())
		return err, ""
	}
	lastIndex := strings.LastIndex(fullName, "ScanImg")
	subPath := ""
	if lastIndex > 0 {
		subPath = fullName[lastIndex:]
	} else {
		return errors.New("文件路径错误"), ""
	}
	subPath = strings.ReplaceAll(subPath, "\\", "/")

	object, err := client.MinIOClient.Client.PutObject(context.Background(), bucketName, subPath,
		fileTemp, fileInfo.Size(), minio.PutObjectOptions{ContentType: "image/tiff"})
	if err != nil {
		log.Println(err)
		utils.Log.Error("上传文件失败" + err.Error())
		return err, ""
	} else {
		if object.ETag != ETag {
			return errors.New("md5不一致,本地的值为:" + ETag + ",远程的值为:" + object.ETag), ""
		}
	}
	return nil, object.ETag
}

func calculateETagFromContent(filePath string) (string, error) {
	content, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", err
	}

	// 计算文件内容的MD5哈希值作为ETag
	etag := fmt.Sprintf("%x", md5.Sum(content))
	return etag, nil
}

func getMacAddress() []string {
	var macList []string
	interfaces, err := net.Interfaces()
	if err != nil {
		fmt.Println("Error fetching network interfaces:", err)
		return nil
	}
	for _, iface := range interfaces {
		// 忽略回环接口
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}
		if strings.HasPrefix(iface.Name, "virbr") ||
			strings.HasPrefix(iface.Name, "VMware") ||
			iface.Name == "lo" || // 排除环回接口
			iface.Name == "docker0" { // 排除Docker接口
			continue
		}
		// 获取MAC地址
		mac := iface.HardwareAddr
		if mac != nil {
			macList = append(macList, mac.String())
		}
	}
	return macList
}
