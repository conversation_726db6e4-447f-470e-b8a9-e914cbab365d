(function(t){function e(e){for(var a,l,i=e[0],u=e[1],s=e[2],p=0,d=[];p<i.length;p++)l=i[p],Object.prototype.hasOwnProperty.call(n,l)&&n[l]&&d.push(n[l][0]),n[l]=0;for(a in u)Object.prototype.hasOwnProperty.call(u,a)&&(t[a]=u[a]);c&&c(e);while(d.length)d.shift()();return o.push.apply(o,s||[]),r()}function r(){for(var t,e=0;e<o.length;e++){for(var r=o[e],a=!0,i=1;i<r.length;i++){var u=r[i];0!==n[u]&&(a=!1)}a&&(o.splice(e--,1),t=l(l.s=r[0]))}return t}var a={},n={app:0},o=[];function l(e){if(a[e])return a[e].exports;var r=a[e]={i:e,l:!1,exports:{}};return t[e].call(r.exports,r,r.exports,l),r.l=!0,r.exports}l.m=t,l.c=a,l.d=function(t,e,r){l.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},l.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},l.t=function(t,e){if(1&e&&(t=l(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(l.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)l.d(r,a,function(e){return t[e]}.bind(null,a));return r},l.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return l.d(e,"a",e),e},l.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},l.p="/";var i=window["webpackJsonp"]=window["webpackJsonp"]||[],u=i.push.bind(i);i.push=e,i=i.slice();for(var s=0;s<i.length;s++)e(i[s]);var c=u;o.push([0,"chunk-vendors"]),r()})({0:function(t,e,r){t.exports=r("56d7")},"31f0":function(t,e,r){"use strict";r("6a5d")},"56d7":function(t,e,r){"use strict";r.r(e);r("4de4"),r("e260"),r("e6cf"),r("cca6"),r("a79d");var a=r("2b0e"),n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("Home")},o=[],l=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",[r("div",{staticClass:"top"},[t._v(" 图像上传监控 ")]),r("div",t._l(t.clients,(function(e){return r("div",{key:e.ip},[r("div",[e&&!e.status?r("div",{staticStyle:{"border-bottom":"1px solid rgba(0, 0, 0, 0.1)",height:"40px","line-height":"40px",color:"red","font-weight":"bold"}},[t._v(" "+t._s(e&&!e.status?e.ip+": 服务器未连接":"")+" ")]):t._e()]),t._l(e.examList,(function(e){return r("div",{key:e.subjectId,staticStyle:{"border-bottom":"#b0bec5 solid 1px"}},[r("div",{staticStyle:{"background-color":"lightsteelblue",height:"40px",display:"flex","align-items":"center"}},[r("span",{staticStyle:{"margin-left":"10px"}},[t._v("考试名称："+t._s(e&&e.length>0?e[0].examName:""))]),r("span",{staticStyle:{"margin-left":"20px"}},[t._v("创建时间："+t._s(e&&e.length>0?e[0].examCreateTime:""))])]),r("el-table",{staticStyle:{width:"100%"},attrs:{data:e}},[r("el-table-column",{attrs:{prop:"subjectName",label:"科目",width:"180"}}),r("el-table-column",{attrs:{prop:"subjectId",label:"科目id",width:"180"}}),r("el-table-column",{attrs:{prop:"uploadAmount",label:"已上传数量",width:"180"}}),r("el-table-column",{attrs:{prop:"unUploadAmount",label:"未上传数量",width:"180"}}),r("el-table-column",{attrs:{prop:"errorAmount",label:"错误数量",width:"180"}}),r("el-table-column",{attrs:{prop:"errorAmount",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"最多显示2000条错误信息",placement:"top-start"}},[e.row.errorAmount>0?r("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(r){return t.errorHandle(e.$index,e.row)}}},[t._v("查看错误 ")]):t._e()],1)]}}],null,!0)})],1),r("div",{staticStyle:{"border-bottom":"#b0bec5 solid 1px"}}),t.showErrorTable?r("el-table",{staticStyle:{height:"250px",overflow:"auto"},attrs:{data:t.errDataList,border:""}},[r("el-table-column",{attrs:{prop:"candidateNo",label:"考号",width:"150px"}}),r("el-table-column",{attrs:{prop:"cardType",label:"卡型",width:"100px"}}),r("el-table-column",{attrs:{prop:"fImageUrl",label:"正面图像",width:"300px"}}),r("el-table-column",{attrs:{prop:"bImageUrl",label:"反面图像",width:"300px"}}),r("el-table-column",{attrs:{prop:"errorMsg",label:"错误消息"}})],1):t._e()],1)}))],2)})),0)])},i=[],u=(r("96cf"),r("1da1")),s={data:function(){return{tableData:[],showErrorTable:!1,errDataList:[],errorMsg:"",clients:[]}},created:function(){},mounted:function(){this.getUploadList()},filters:{},methods:{errorHandle:function(t,e){this.showErrorTable?this.showErrorTable=!1:(this.showErrorTable=!0,this.getErrorDataList(e))},getErrorDataList:function(t){var e=this;return Object(u["a"])(regeneratorRuntime.mark((function r(){var a,n;return regeneratorRuntime.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$http.get("/getErrorDataList",{params:{subjectId:t.subjectId,examId:t.examId,dbId:t.dbId}});case 2:a=r.sent,n=a.data,e.errDataList=n.data;case 5:case"end":return r.stop()}}),r)})))()},getUploadList:function(){var t=this;return Object(u["a"])(regeneratorRuntime.mark((function e(){var r,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$http.get("/getClients",{});case 2:r=e.sent,a=r.data,t.clients=a.data;case 5:case"end":return e.stop()}}),e)})))()}}},c=s,p=(r("31f0"),r("2877")),d=Object(p["a"])(c,l,i,!1,null,"2819c0f0",null),f=d.exports,b={components:{Home:f}},h=b,m=Object(p["a"])(h,n,o,!1,null,null,null),v=m.exports,g=r("8c4f");a["default"].use(g["a"]);var w=g["a"].prototype.push;g["a"].prototype.push=function(t){return w.call(this,t).catch((function(t){return t}))};var y=[],x=new g["a"]({mode:"history",base:"/",routes:y});x.beforeEach((function(t,e,r){t.meta.title&&(document.title=t.meta.title?t.meta.title:"加载中"),r()}));var _=x,j=r("f309"),O=r("352b"),S=r.n(O);S.a.config({top:60,duration:3e3,zIndex:2e3}),a["default"].prototype.$message=S.a,a["default"].use(j["a"]);var k=new j["a"]({breakpoint:{mobileBreakpoint:"sm"}}),E=r("5a0c"),I=r.n(E),L=r("bc3a"),T=r.n(L);T.a.defaults.baseURL="http://localhost:8566/api",a["default"].prototype.$http=T.a;var D=r("5c96"),P=r.n(D);r("0fae");a["default"].filter("dateformat",(function(t,e){return I()(t).format(e)})),a["default"].use(P.a),new a["default"]({router:_,vuetify:k,el:"#app",render:function(t){return t(v)}}),a["default"].config.productionTip=!1},"6a5d":function(t,e,r){}});