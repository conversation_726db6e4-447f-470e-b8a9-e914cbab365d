{"name": "front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.0", "core-js": "^3.6.5", "dayjs": "^1.10.7", "element-ui": "^2.15.14", "vue": "^2.6.11", "vue-multiple-message": "^1.1.0", "vue-router": "^3.2.0", "vuetify": "^2.4.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "sass": "^1.19.0", "sass-loader": "^8.0.0", "vue-cli-plugin-vuetify": "~2.0.8", "vue-template-compiler": "^2.6.11", "vuetify-loader": "^1.3.0"}}