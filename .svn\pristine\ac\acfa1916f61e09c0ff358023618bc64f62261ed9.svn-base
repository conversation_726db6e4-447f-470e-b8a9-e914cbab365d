package api

import (
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/wejectchen/ginblog/job"
	response "github.com/wejectchen/ginblog/model"
	"github.com/wejectchen/ginblog/utils"
	"strconv"
)

type StaticService struct {
}
type StaticInfo struct {
	SubjectId      int    `json:"subjectId"`
	SubjectName    string `json:"subjectName"`
	ExamName       string `json:"examName"`
	UnUploadAmount int    `json:"unUploadAmount"`
	UploadAmount   int    `json:"uploadAmount"`
	ErrorAmount    int    `json:"errorAmount"`
	ExamId         int    `json:"examId"`
	DbId           string `json:"dbId"`
	ExamCreateTime string `json:"examCreateTime"`
}

func (this *StaticService) getExamList(uploadClient *job.UploadClient) []job.Exam {
	client := uploadClient.HttpClient
	params := make(map[string]string)
	params["pageNum"] = "1"
	params["pageSize"] = "100000"
	result, err := client.Get("/base/exam/list", params)
	if err != nil {
		uploadClient.HttpClient.Token = ""
		uploadClient.IsLogin = false
		fmt.Println("get exam list error1")
		return nil
	}
	var examResult job.ExamResult
	err = json.Unmarshal(result.Body(), &examResult)
	if err != nil {
		fmt.Println("get exam list error2")
		return nil
	}
	if examResult.Code == 200 {
		return examResult.Rows
	} else {
		fmt.Println("get exam list error3")
		return nil
	}
}

func (this *StaticService) existTable(tableName string) bool {
	var count int64
	err := job.G_SqlLiteClient.DB.Raw("select count(1) from sqlite_master where type='table' and name = ?", tableName).Count(&count).Error
	if err != nil || count == 0 {
		return false
	} else {
		return true
	}
}

func (this *StaticService) getStaticByExam(tableNameDo, tableNameUndo, tableNameErr string) []StaticInfo {
	var staticInfos []StaticInfo
	err := job.G_SqlLiteClient.DB.Raw("SELECT ifnull(a.amount,0)-ifnull(b.amount,0)-IFNULL(c.amount,0)  AS un_upload_amount, ifnull(b.amount,0) AS upload_amount," +
		"IFNULL(c.amount,0) AS error_amount,a.subject_id,a.subject_name FROM ( SELECT count( 1 ) amount, subject_id,subject_name FROM " + tableNameUndo + " GROUP BY subject_id ) AS a " +
		"LEFT JOIN ( SELECT count( 1 ) amount, subject_id FROM " + tableNameDo + " GROUP BY subject_id ) AS b ON a.subject_id = b.subject_id " +
		"LEFT JOIN ( SELECT count( 1 ) amount, subject_id FROM " + tableNameErr + " GROUP BY subject_id ) AS c on a.subject_id = c.subject_id").Scan(&staticInfos).Error
	if err != nil {
		fmt.Println("get static info error", err)
		return nil
	}
	return staticInfos
}

type ClientStatus struct {
	Ip       string         `json:"ip"`
	Status   bool           `json:"status"`
	ErrorMsg string         `json:"errorMsg"`
	ExamList [][]StaticInfo `json:"examList"`
}

type TableInfo struct {
	Name string `json:"name"`
}

func (this *StaticService) getDBIdMap(uploadClient job.UploadClient) (string, error) {
	url := "/system/config/list"
	params := make(map[string]string)
	params["pId"] = "0"
	params["configKey"] = "scan.exam.dbid"
	resp, err := uploadClient.HttpClient.Get(url, params)
	if err != nil {
		return "", err
	} else {
		var configList job.SysConfigs
		err := json.Unmarshal(resp.Body(), &configList)
		if err != nil {
			fmt.Println("get db id list error")
			return "", err
		} else {
			return configList.Rows[0].ConfigValue, nil
		}
	}
}

func (this *StaticService) GetClients(c *gin.Context) {
	var clients []ClientStatus
	for s := range job.G_Clients {
		client := job.G_Clients[s]
		if !client.IsLogin {
			var stat = ClientStatus{
				Ip:       client.Ip,
				Status:   client.IsLogin,
				ExamList: nil,
			}
			clients = append(clients, stat)
			continue
		}
		var staticInfoList = make([][]StaticInfo, 0)
		examList := this.getExamList(client)
		dbId, err := this.getDBIdMap(*client)
		if err != nil {
			fmt.Println("get db id error", err)
			var stat = ClientStatus{
				Ip:       client.Ip,
				Status:   client.IsLogin,
				ErrorMsg: "获取dbId失败",
				ExamList: nil,
			}
			clients = append(clients, stat)
			continue
		}
		for i := range examList {
			exam := examList[i]
			tableNameDo := "db_" + dbId + "_" + strconv.Itoa(exam.ExamId) + "_do"
			tableNameUnDo := "db_" + dbId + "_" + strconv.Itoa(exam.ExamId) + "_undo"
			tableNameErr := "db_" + dbId + "_" + strconv.Itoa(exam.ExamId) + "_error"
			if !this.existTable(tableNameUnDo) {
				continue
			}
			examInfo := this.getStaticByExam(tableNameDo, tableNameUnDo, tableNameErr)
			if examInfo == nil {
				continue
			}
			for j := range examInfo {
				examInfo[j].ExamName = exam.ExamName
				examInfo[j].ExamId = exam.ExamId
				examInfo[j].DbId = dbId
				examInfo[j].ExamCreateTime = exam.CreateTime
			}
			staticInfoList = append(staticInfoList, examInfo)

		}
		var stat = ClientStatus{
			Ip:       client.Ip,
			Status:   client.IsLogin,
			ExamList: staticInfoList,
		}
		clients = append(clients, stat)
	}
	response.OkWithData(clients, c)
}

type ErrorData struct {
	ExamId      int    `json:"examId"`
	SubjectId   int    `json:"subjectId"`
	ErrorMsg    string `json:"errorMsg"`
	CandidateNo string `json:"candidateNo"`
	CardType    string `json:"cardType"`
	FImageUrl   string `json:"fImageUrl"`
	BImageUrl   string `json:"bImageUrl"`
}

func (this *StaticService) GetErrorDataList(context *gin.Context) {
	dbId := context.Query("dbId")
	examId := context.Query("examId")
	subjectId := context.Query("subjectId")
	tableNameErr := "db_" + dbId + "_" + examId + "_error"
	var errorDataList []ErrorData
	if !this.existTable(tableNameErr) {
		response.OkWithData(errorDataList, context)
		return
	}
	err := job.G_SqlLiteClient.DB.Raw("SELECT subject_id,error_msg,candidate_no,card_type,f_image_url,b_image_url FROM "+tableNameErr+" where subject_id=? limit 2000", subjectId).Scan(&errorDataList).Error
	if err != nil {
		fmt.Println("get error data list error", err)
		response.FailWithMessage("获取错误数据失败", context)
		return
	}
	for i := range errorDataList {
		errorDataList[i].FImageUrl = utils.LocalImgPath + errorDataList[i].FImageUrl
		errorDataList[i].BImageUrl = utils.LocalImgPath + errorDataList[i].BImageUrl
	}
	response.OkWithData(errorDataList, context)
}
