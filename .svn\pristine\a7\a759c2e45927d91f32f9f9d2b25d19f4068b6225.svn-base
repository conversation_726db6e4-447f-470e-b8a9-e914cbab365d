package utils

import (
	"fmt"
	retalog "github.com/lestrrat-go/file-rotatelogs"
	"github.com/rifflock/lfshook"
	"github.com/sirupsen/logrus"
	"os"
	"time"
)

type LogTool struct {
	logger *logrus.Logger
}

func NewLogTool() *LogTool {
	filePath := "uploader.log"
	scr, err := os.OpenFile(filePath, os.O_RDWR|os.O_CREATE, 0755)
	if err != nil {
		fmt.Println("err:", err)
	}

	logger := logrus.New()
	logger.Out = scr
	logger.SetLevel(logrus.InfoLevel)
	logWriter, _ := retalog.New(
		filePath+"%Y%m%d.log",
		retalog.WithMaxAge(7*24*time.Hour),
		retalog.WithRotationTime(24*time.Hour),
	)

	writeMap := lfshook.WriterMap{
		logrus.InfoLevel:  logWriter,
		logrus.FatalLevel: logWriter,
		logrus.DebugLevel: logWriter,
		logrus.WarnLevel:  logWriter,
		logrus.ErrorLevel: logWriter,
		logrus.PanicLevel: logWriter,
	}
	Hook := lfshook.NewHook(writeMap, &logrus.TextFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
	})

	logger.AddHook(Hook)
	return &LogTool{logger: logger}
}

// Info 使用info级别记录日志
func (l *LogTool) Info(msg string) {
	l.logger.Info(msg)
}

// Warn 使用warning级别记录日志
func (l *LogTool) Warn(msg string) {
	l.logger.Warn(msg)
}

// Error 使用error级别记录日志
func (l *LogTool) Error(msg string) {
	l.logger.Error(msg)
}

// Debug 使用debug级别记录日志
func (l *LogTool) Debug(msg string) {
	l.logger.Debug(msg)
}

var Log = NewLogTool()
