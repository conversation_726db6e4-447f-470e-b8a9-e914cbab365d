<template>
    <div>
        <div class="top">
            图像上传监控
        </div>
        <div>
            <div v-for="item in clients" :key="item.ip">
                <div>
                    <div v-if="item&&!item.status" style="border-bottom: 1px solid rgba(0, 0, 0, 0.1);height: 40px;line-height: 40px;color:red;font-weight: bold">
                        {{item&&!item.status?item.ip+': 服务器未连接':''}}
                    </div>
                </div>
                <div v-for="subItem in item.examList" :key="subItem.subjectId" style="border-bottom: #b0bec5 solid 1px">
                    <div style="background-color: lightsteelblue;height: 40px;display: flex;align-items: center;">
                        <span style="margin-left: 10px">考试名称：{{subItem&&subItem.length>0?subItem[0].examName:''}}</span>
                        <span style="margin-left: 20px">创建时间：{{subItem&&subItem.length>0?subItem[0].examCreateTime:''}}</span>
                        <el-button @click="refresh">刷新</el-button>
                    </div>
                    <el-table
                            :data="subItem"
                            style="width: 100%">
                        <el-table-column
                                prop="subjectName"
                                label="科目"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="subjectId"
                                label="科目id"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="uploadAmount"
                                label="已上传数量" width="180">
                        </el-table-column>
                        <el-table-column
                                prop="unUploadAmount"
                                label="未上传数量" width="180">
                        </el-table-column>
                        <el-table-column
                                prop="errorAmount"
                                label="错误数量" width="180">
                        </el-table-column>
                        <el-table-column
                                prop="errorAmount"
                                label="操作">
                            <template slot-scope="scope">
                                <el-tooltip class="item" effect="dark" content="最多显示2000条错误信息" placement="top-start">
                                <el-button
                                        size="mini"
                                        type="danger"
                                        v-if="scope.row.errorAmount>0"
                                        @click="errorHandle(scope.$index, scope.row,subItem)">查看错误
                                </el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                    <div style="border-bottom: #b0bec5 solid 1px "></div>
                    <el-table v-if="subItem.showErrorTable"
                              :data="subItem.errDataList"
                              border
                              style="height: 250px;overflow: auto">
                        <el-table-column
                                prop="candidateNo"
                                label="考号" width="150px">
                        </el-table-column>
                        <el-table-column
                                prop="cardType"
                                label="卡型" width="100px" >
                        </el-table-column>
                        <el-table-column
                                prop="fImageUrl"
                                label="正面图像" width="300px" >
                        </el-table-column>
                        <el-table-column
                                prop="bImageUrl"
                                label="反面图像" width="300px" >
                        </el-table-column>
                        <el-table-column
                                prop="errorMsg"
                                label="错误消息" >
                        </el-table-column>
                    </el-table>
                </div>
            </div>
        </div>
    </div>

</template>

<script>
  export default {
    data() {
      return {
        showErrorTable: false,
        errDataList: [],
        errorMsg: '',
        clients: []
      }
    },
    created() {
    },
    mounted() {
      this.getUploadList()
    },
    filters: {},
    methods: {
      refresh() {
        this.getUploadList()
      },
      errorHandle(i, row,subItem) {
        if(subItem.showErrorTable){
          subItem.showErrorTable = false;
        }
        else {
          subItem.showErrorTable = true
          this.getErrorDataList(row,subItem)
        }
        let clients = this.clients
        this.clients = []
        this.clients = clients
      },
      async getErrorDataList(row,subItem){
        const { data: res } = await this.$http.get('/getErrorDataList', {
          params: {
            subjectId: row.subjectId,
            examId: row.examId,
            dbId: row.dbId
          }
        })
        subItem.errDataList = res.data
        let clients = this.clients
        this.clients = []
        this.clients = clients
      },
      async getUploadList() {
        const { data: res2 } = await this.$http.get('/getClients', {})
        this.clients = res2.data
      }
    }
  }
</script>
<style scoped type="scss">
    .top{
        height: 60px;
        background-color: #e9eef3;
        color: #333;
        font-size: 26px;
        text-align: center;
        line-height: 60px
    }
</style>
