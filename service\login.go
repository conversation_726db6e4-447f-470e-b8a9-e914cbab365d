package service

import (
	"encoding/json"
	"fmt"
	"github.com/wejectchen/ginblog/middleware"
	"github.com/wejectchen/ginblog/utils"
)

type loginParam struct {
	ClientType string `json:"clientType"`
	Password   string `json:"password"`
	Role<PERSON><PERSON>    string `json:"roleKey"`
	VerifyCode string `json:"verifyCode"`
	UserName   string `json:"userName"`
}
type LoginUser struct {
	Msg  string `json:"msg"`
	Code int    `json:"code"`
	Data struct {
		SearchValue interface{} `json:"searchValue"`
		CreateBy    string      `json:"createBy"`
		CreateTime  string      `json:"createTime"`
		UpdateBy    interface{} `json:"updateBy"`
		UpdateTime  interface{} `json:"updateTime"`
		Remark      interface{} `json:"remark"`
		Params      struct {
		} `json:"params"`
		UserId            int         `json:"userId"`
		DeptId            interface{} `json:"deptId"`
		UserName          string      `json:"userName"`
		NickName          string      `json:"nickName"`
		Email             string      `json:"email"`
		Phonenumber       string      `json:"phonenumber"`
		Sex               string      `json:"sex"`
		Avatar            string      `json:"avatar"`
		Salt              interface{} `json:"salt"`
		Status            string      `json:"status"`
		DelFlag           string      `json:"delFlag"`
		LoginIp           string      `json:"loginIp"`
		LoginDate         string      `json:"loginDate"`
		LoginErrorCount   int         `json:"loginErrorCount"`
		ClientId          int         `json:"clientId"`
		OrgId             int         `json:"orgId"`
		RoleNames         interface{} `json:"roleNames"`
		OrgCode           interface{} `json:"orgCode"`
		ClientName        interface{} `json:"clientName"`
		PwdLastUpdateTime interface{} `json:"pwdLastUpdateTime"`
		ReservedUser      interface{} `json:"reservedUser"`
		UserType          int         `json:"userType"`
		Token             string      `json:"token"`
		ExpireTime        interface{} `json:"expireTime"`
		LoginLocation     interface{} `json:"loginLocation"`
		Browser           interface{} `json:"browser"`
		Os                interface{} `json:"os"`
	} `json:"data"`
}

func DoLogin(client *middleware.RestyClient) bool {
	var password = "60a130eadcf591fb43f10f5e48e26cb6"
	var roleKey = "scan_auto_test"
	var userName = "cutadmin"
	var verifyCode = "1234"
	var clientType = "app"

	params := map[string]string{}
	params["clientType"] = clientType
	params["password"] = password
	params["roleKey"] = roleKey
	params["verifyCode"] = verifyCode
	params["userName"] = userName

	result, err := client.PostBodyNoToken("/auto/login/safe", params)
	if err != nil {
		fmt.Println(err)
		utils.Log.Error(err.Error())
		return false
	}
	var loginUser LoginUser
	err = json.Unmarshal(result.Body(), &loginUser)
	if err != nil {
		fmt.Println(err)
		utils.Log.Error(err.Error())
		return false
	}
	fmt.Println("用户登陆" + client.Url + "成功")
	utils.Log.Info("用户登陆" + client.Url + "成功")
	client.SetToken(loginUser.Data.Token)
	return true
}
