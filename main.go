package main

import (
	"fmt"
	"image/jpeg"
	"log"
	"net"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/glebarez/sqlite"
	"github.com/go-co-op/gocron"
	"github.com/wejectchen/ginblog/job"
	"github.com/wejectchen/ginblog/middleware"
	"github.com/wejectchen/ginblog/routes"
	"github.com/wejectchen/ginblog/utils"
	"golang.org/x/image/tiff"
	"gorm.io/gorm"
)

var httpClients []middleware.RestyClient
var minIOClients []middleware.MinioClient

type Mytest struct {
	Id    int64
	Name  string
	Name2 string
}

type User struct {
	Id    int64
	Name  string
	Name2 string
}

func saveToJpg(path string) (error, string) {
	// 打开TIFF文件
	tiffFile, err := os.Open(path)
	if err != nil {
		log.Fatalf("无法打开TIFF文件: %v", err)
	}
	defer tiffFile.Close()

	// 解码TIFF文件
	img, err := tiff.Decode(tiffFile)
	if err != nil {
		log.Fatalf("无法解码TIFF文件: %v", err)
	}

	fileName := filepath.Base(path)
	ext := filepath.Ext(fileName)
	name := fileName[:len(fileName)-len(ext)]

	// 新文件路径
	newFilePath := filepath.Join(filepath.Dir(path), name+".jpg")

	// 创建JPG文件
	jpgFile, err := os.Create(newFilePath)
	if err != nil {
		log.Fatalf("无法创建JPG文件: %v", err)
	}
	defer jpgFile.Close()

	// 将图像编码为JPG格式并保存到文件
	err = jpeg.Encode(jpgFile, img, nil)
	if err != nil {
		log.Fatalf("无法将图像编码为JPG格式: %v", err)
	}
	return nil, newFilePath
}

func sqlLiteTest2() {
	dsn := filepath.Join(".", "test", "test.db")

	// 连接到SQLite数据库
	db, err := gorm.Open(sqlite.Open(dsn), &gorm.Config{})
	if err != nil {
		panic("failed to connect database")
	}
	for i := 0; i < 50000; i++ {
		var a User = User{Name: "aaaa_" + strconv.Itoa(i), Name2: "bbb_" + strconv.Itoa(i)}
		db.Save(&a)
		fmt.Println(a)
	}
}

func getMacAddress() []string {
	var macList []string
	interfaces, err := net.Interfaces()
	if err != nil {
		fmt.Println("Error fetching network interfaces:", err)
		return nil
	}
	for _, iface := range interfaces {
		// 忽略回环接口
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}
		if strings.HasPrefix(iface.Name, "virbr") ||
			strings.HasPrefix(iface.Name, "VMware") ||
			iface.Name == "lo" || // 排除环回接口
			iface.Name == "docker0" { // 排除Docker接口
			continue
		}
		// 获取MAC地址
		mac := iface.HardwareAddr
		if mac != nil {
			macList = append(macList, mac.String())
		}
	}
	return macList
}

func main() {
	// 引用数据库
	//model.InitDb()
	utils.Init()
	job.G_SqlLiteClient = &middleware.SqlLiteClient{}
	job.G_SqlLiteClient.InitDB(filepath.Join(utils.LocalImgPath, "xrbScanLocalImg.db"))

	scheduler := gocron.NewScheduler(time.UTC)
	aJob := job.UploadJob{}
	scheduler.Every(5).Second().Do(aJob.Upload)
	scheduler.StartAsync()

	//scheduler = gocron.NewScheduler(time.UTC)
	//scheduler.Every(10).Second().Do(uploadJob.UploadTest)
	//scheduler.StartAsync()
	// 引入路由组件
	routes.InitRouter()
}
