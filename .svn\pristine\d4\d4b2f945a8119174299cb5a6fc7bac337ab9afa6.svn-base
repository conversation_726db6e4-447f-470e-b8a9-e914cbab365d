package routes

import (
	"fmt"

	"github.com/gin-contrib/multitemplate"
	"github.com/gin-gonic/gin"
	"github.com/wejectchen/ginblog/api"
	"github.com/wejectchen/ginblog/middleware"
	"github.com/wejectchen/ginblog/utils"
)

func createMyRender() multitemplate.Renderer {
	p := multitemplate.NewRenderer()
	//p.AddFromFiles("admin", "web/admin/dist/index.html")
	p.AddFromFiles("front", "web/front/index.html")
	return p
}

func InitRouter() {
	gin.SetMode(utils.AppMode)
	r := gin.New()
	// 设置信任网络 []string
	// nil 为不计算，避免性能消耗，上线应当设置
	_ = r.SetTrustedProxies(nil)

	r.HTMLRender = createMyRender()
	r.Use(middleware.Logger())
	r.Use(gin.Recovery())
	r.Use(middleware.Cors())

	//r.St<PERSON>("/static", "./web/admin")
	r.St<PERSON>("/static", "web/front/static")
	r.StaticFile("/favicon.ico", "/web/front/favicon.ico")

	r.GET("/", func(c *gin.Context) {
		c.HTML(200, "front", nil)
	})

	var staticService = api.StaticService{}
	router := r.Group("api")
	{
		// 用户信息模块
		router.GET("getErrorDataList", staticService.GetErrorDataList)
		router.GET("getClients", staticService.GetClients)
	}

	fmt.Printf("服务启动成功，端口%s\n", utils.HttpPort)
	_ = r.Run(utils.HttpPort)
}
