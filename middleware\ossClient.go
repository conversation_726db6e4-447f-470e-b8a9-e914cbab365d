package middleware

import (
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/google/uuid"
	"io"
	"os"
	"path/filepath"
)

type OSSClient struct {
	Endpoint        string
	AccessKeyID     string
	SecretAccessKey string
	BucketName      string
	Client          *oss.Client
	Bucket          *oss.Bucket
}

func (this *OSSClient) InitClient() {
	var err error
	this.Client, err = oss.New(this.Endpoint, this.AccessKeyID, this.SecretAccessKey)
	// 获取Bucket实例
	this.Bucket, err = this.Client.Bucket(this.BucketName)
	if err != nil {
		panic(err)
	}
	if err != nil {
		panic(err)
	}
}

func calculateMD5(content string) string {
	h := md5.New()
	h.Write([]byte(content))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func (oc *OSSClient) PutObjectAndValidateMd5(objectName, localFileName string) (error, string) {
	// 打开本地文件
	fd, err := os.Open(localFileName)
	if err != nil {
		return fmt.Errorf("failed to open file: %v", err), ""
	}
	defer fd.Close()

	// 设置Content-MD5头信息（重要）
	oldMd5, err := calculateFileMD5(localFileName)
	if err != nil {
		return err, ""
	}

	// 上传文件到OSS
	err = oc.Bucket.PutObject(objectName, fd, oss.ContentMD5(oldMd5))
	if err != nil {
		println(err.Error())
		return fmt.Errorf("failed to upload file: %v", err.Error()), ""
	}

	fmt.Printf("File %s uploaded to %s as %s\n", localFileName, oc.Bucket.BucketName, objectName)
	return nil, oldMd5
}

func (oc *OSSClient) GetObject(objectName, localFileName string) error {
	// 下载文件到本地
	err := oc.Bucket.GetObjectToFile(objectName, localFileName)
	if err != nil {
		return fmt.Errorf("failed to download file: %v", err)
	}
	//fmt.Printf("File %s downloaded from %s as %s\n", objectName, oc.Bucket.BucketName, localFileName)
	return nil
}

func calculateFileMD5(filePath string) (string, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 创建新的MD5哈希计算器
	hash := md5.New()

	// 将文件内容复制到哈希计算器中
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(hash.Sum(nil)), nil
}

func (oc *OSSClient) CheckMd5(fileName string, remoteFileName, md5 string) error {
	// 获取目录部分
	dir := filepath.Dir(fileName)
	// 获取文件名部分
	base := filepath.Base(fileName)
	newUUID, err := uuid.NewUUID()
	if err != nil {
		return err
	}
	localFileName := filepath.Join(dir, base+newUUID.String())
	err = oc.GetObject(remoteFileName, localFileName)
	if err != nil {
		return err
	}
	fileMD5, err := calculateFileMD5(localFileName)
	if err != nil {
		return err
	}
	if fileMD5 == "" {
		return fmt.Errorf("failed to calculate MD5")
	}
	if fileMD5 != md5 {
		return fmt.Errorf("MD5值不匹配" + fileName)
	}
	os.Remove(localFileName)
	return nil
}
