package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/wejectchen/ginblog/middleware"
	"github.com/wejectchen/ginblog/model"
)

func UpdateUploadStatus(client *middleware.RestyClient, candidateNo, scanImgServerIp, subjectId, scanBatchNo string) error {
	params := map[string]string{}
	params["candidateNo"] = candidateNo
	params["subjectId"] = subjectId
	params["scanImgServerIp"] = scanImgServerIp
	params["imgState"] = "9"
	params["scanBatchNo"] = scanBatchNo
	result, err := client.PostBody("/candidate/scan/updateUploadStatus", params)
	if err != nil {
		fmt.Println(err)
		return err
	}
	var res model.Response
	err = json.Unmarshal(result.Body(), &res)
	if err != nil {
		return err
	}
	if res.Code != 200 {
		fmt.Println(res)
		return errors.New(res.Msg + ",code:" + string(res.Code))
	}
	return nil
}
