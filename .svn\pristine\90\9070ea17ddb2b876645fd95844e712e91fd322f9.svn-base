import Vue from 'vue'
import VueRouter from 'vue-router'

// const ArticleList = () =>
//   import(/* webpackChunkName: "group-index" */ '../components/ArticleList.vue')
// const Category = () =>
//   import(/* webpackChunkName: "group-category" */ '../components/CateList.vue')

Vue.use(VueRouter)

//获取原型对象上的push函数
const originalPush = VueRouter.prototype.push
//修改原型对象中的push方法
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [

]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title ? to.meta.title : '加载中'
  }
  next()
})

export default router
