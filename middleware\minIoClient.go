package middleware

import (
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

type MinioClient struct {
	Endpoint        string
	AccessKeyID     string
	SecretAccessKey string
	UseSSL          bool
	Client          *minio.Client
}

func (this *MinioClient) InitMinioClient() {
	var err error
	this.Client, err = minio.New(this.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(this.AccessKeyID, this.SecretAccessKey, ""),
		Secure: this.UseSSL,
	})
	if err != nil {
		panic(err)
	}
}
