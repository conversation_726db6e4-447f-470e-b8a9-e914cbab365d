package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"gopkg.in/ini.v1"
)

var (
	AppMode      string
	HttpPort     string
	ServerUrls   []string
	LocalImgPath string
	ExecDir      string
	BaseDir      string
)

// Init 初始化
func Init() {
	/*
		BaseDir = "C:\\SeaSkyLand\\xrbScanClient"
		configPath := filepath.Join(BaseDir+"\\miniouploadserver\\", "config", "config.ini")
	*/
	//兼容Linux目录
	execPath, _ := os.Executable()
	ExecDir = filepath.Dir(execPath)
	BaseDir = filepath.Dir(ExecDir)
	configPath := filepath.Join(ExecDir, "config", "config.ini")

	file, err := ini.Load(configPath)
	if err != nil {
		fmt.Println("配置文件读取错误，请检查文件路径:", err)
	}
	loadServer(file)

	//sysParamPath := filepath.Join(BaseDir, "\\SysParam.ini")
	//兼容Linux目录
	sysParamPath := filepath.Join(BaseDir, "SysParam.ini")
	// 配置 ini 加载选项，禁用分号作为注释符号
	cfg := ini.LoadOptions{
		IgnoreInlineComment: true,
	}
	file, err = ini.LoadSources(cfg, sysParamPath)
	if err != nil {
		fmt.Println("配置文件读取错误，请检查文件路径:", err)
	} else {
		loadIni(file)
	}
}

func loadServer(file *ini.File) {
	AppMode = file.Section("server").Key("AppMode").MustString("debug")
	HttpPort = file.Section("server").Key("HttpPort").MustString(":8566")
}

func removePortFromHost(input string) (string, string, error) {
	// 分割字符串以获取协议和主机部分（可能包含端口）
	parts := strings.SplitN(input, "//", 2)
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid URL format: %s", input)
	}

	// 获取主机部分（可能包含端口）
	hostPart := parts[1]

	// 进一步分割主机部分以获取主机名和可能的端口号
	hostAndPort := strings.SplitN(hostPart, ":", 2)
	if len(hostAndPort) == 1 {
		// 没有端口号的情况，直接返回主机名
		return hostPart, "", nil
	} else if len(hostAndPort) == 2 {
		// 有端口号的情况，返回不含端口的主机名
		return hostAndPort[0], hostAndPort[1], nil
	}

	// 理论上这里不会到达，因为SplitN已经确保了最多分割成两部分
	return "", "", fmt.Errorf("unexpected URL format after splitting: %s", input)
}

func loadIni(file *ini.File) {
	str := file.Section("System").Key("DataIP").String()
	if str == "" {
		fmt.Println("配置文件读取错误，请检查文件路径:")
	}
	tempList := strings.Split(str, ";")
	flag := false
	for i := range tempList {
		ip := tempList[i]
		//ipNew,port, err := removePortFromHost(ip)
		ip = strings.ReplaceAll(ip, "http://", "")
		ip = strings.ReplaceAll(ip, "https://", "")
		ipNew := strings.Split(ip, ":")[0]
		if (ipNew == "127.0.0.1" || ipNew == "localhost") && flag == true {
			continue
		}
		if (ipNew == "127.0.0.1" || ipNew == "localhost") && flag == false {
			flag = true
		}
		tempList[i] = strings.Replace(tempList[i], ":80", "", -1)
		tempList[i] = strings.Replace(tempList[i], ":443", "", -1)
		ServerUrls = append(ServerUrls, tempList[i])
	}
	LocalImgPath = file.Section("System").Key("LocalImgPath").MustString("")
}
