package middleware

import (
	"github.com/go-resty/resty"
	"time"
)

type RestyClient struct {
	Url    string
	Client *resty.Client
	Token  string
}

func (this *RestyClient) NewClient() {
	this.Client = resty.New()
	this.Client.SetRetryCount(1).SetRetryWaitTime(1 * time.Second)
}

func (this *RestyClient) SetToken(t string) {
	this.Token = t
}

func (this *RestyClient) Get(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").SetHeader("Authorization", this.Token).SetHeader("Clienttype", "app").Get(url)
}

func (this *RestyClient) GetNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").Get(url)
}

func (this *RestyClient) Post(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").SetHeader("Authorization", this.Token).SetHeader("Clienttype", "app").Post(url)
}

func (this *RestyClient) PostNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetQueryParams(p).SetHeader("Accept", "application/json").Post(url)
}

func (this *RestyClient) PostBody(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetBody(p).SetHeader("Accept", "application/json").SetHeader("Authorization", this.Token).SetHeader("Clienttype", "app").Post(url)
}

func (this *RestyClient) PostBodyList(url string, p interface{}) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetBody(p).SetHeader("Accept", "application/json").
		SetHeader("Content-Type", "application/json").SetHeader("Authorization", this.Token).
		SetHeader("Clienttype", "app").Post(url)
}

func (this *RestyClient) PostBodyNoToken(url string, p map[string]string) (*resty.Response, error) {
	url = this.Url + url
	return this.Client.R().SetBody(p).SetHeader("Accept", "application/json").Post(url)
}
